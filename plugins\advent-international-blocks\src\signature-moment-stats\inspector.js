import { InspectorControls } from '@wordpress/block-editor';
import {
	ColorPalette,
	Panel,
	PanelBody,
	PanelRow,
} from '@wordpress/components';
import MediaSelector from '../utils/components/MediaSelector';

export default function ({ attributes, setAttributes }) {
	return (
		<InspectorControls>
			<Panel>
				<PanelBody title="Media Settings">
					<PanelRow>
						<MediaSelector
							attribute="image"
							size="full"
							callback={setAttributes}
							description="Select background image."
						/>
					</PanelRow>
				</PanelBody>
			</Panel>
			<Panel>
				<PanelBody title="Text Settings">
					<PanelRow>
						<ColorPalette
							colors={attributes.textColorOptions}
							value={
								attributes.textColorOptions.find(
									(option) =>
										option.name === attributes.textColor
								).color
							}
							onChange={(color) =>
								setAttributes({
									textColor: attributes.textColorOptions.find(
										(option) => option.color === color
									).name,
								})
							}
						/>
					</PanelRow>
				</PanelBody>
			</Panel>
		</InspectorControls>
	);
}
