import { InnerBlocks, useBlockProps } from '@wordpress/block-editor';
import BackgroundImageStyle from '../utils/components/BackgroundImageStyle';

export default function ({ attributes }) {
	const { className, uniqueClassName } = attributes;

	const classNames = [
		className || '',
		`wp-block-advent-signature-moment-stats--animate alignfull`,
		uniqueClassName,
		`wp-block-advent-signature-moment-stats--has-${attributes.textColor.toLowerCase()}-text-color`,
	];

	const blockProps = useBlockProps.save({
		className: classNames.filter(Boolean).join(' '),
	});

	return (
		<div {...blockProps}>
			<div className="wp-block-advent-signature-moment-stats__foreground">
				<div className="wp-block-advent-signature-moment-stats__animation"></div>
				<InnerBlocks.Content />
				<BackgroundImageStyle
					selectorClass={uniqueClassName}
					url={attributes.image?.url}
				/>
			</div>
		</div>
	);
}
