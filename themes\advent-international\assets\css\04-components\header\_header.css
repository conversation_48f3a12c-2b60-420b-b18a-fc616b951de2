/* ==========================================================================
HEADER
========================================================================== */

.c-site-header {
	@mixin transition transform;
	position: sticky;
	top: 0;
	left: 0;
	z-index: 1000;
	background-color: color(white);
	padding: spacing(s) 0;
	width: 100%;

	.container {
		gap: spacing(s);
	}
}

.c-site-header__upper,
.c-site-header__nav {
	position: relative;
	z-index: 2;
}

.c-site-header__upper {
	.container {
		@mixin grid-columns 2, auto;
		display: grid;
		justify-content: space-between;
	}
}

.c-site-header__nav {
	--sub-menu-width: 35%;
	--row-gap: spacing(xl);
	--bottom-padding: spacing(xl);
	display: grid;
	position: absolute;
	position: fixed;
	top: var(--header-height); /* Header height */
	right: 0;
	bottom: 0;
	left: -100vw;
	grid-auto-flow: row;
	align-content: flex-start;
	gap: calc(var(--row-gap) + spacing(m));
	opacity: 0;
	transition:
		opacity 800ms 0ms transition(custom-bezier-function),
		left 0ms 800ms transition(custom-bezier-function);
	padding-top: calc(var(--row-gap) / 2);
	padding-bottom: var(--bottom-padding);
	width: 100%;
	max-width: 100vw;
	height: auto;
	overflow-x: hidden;
	overflow-y: scroll;

	@media (--tablet-landscape-up) {
		--row-gap: spacing(2-xl);
		gap: calc(var(--row-gap) + spacing(l));
		padding-top: var(--row-gap);
	}

	.container {
		position: relative;
		margin-right: auto;
		margin-left: auto;
		width: 100%;
		max-width: calc(rem(1208px) + (grid(gutter) * 2));

		&.upper {
			display: grid;
			grid-auto-flow: row;
			gap: var(--row-gap);

			@media (--tablet-landscape-up) {
				grid-template-columns: 1fr var(--sub-menu-width);
			}
		}
	}
}

/* Light Header - blue background color, white logo and links */
body.home,
body:has(
		.entry-content
			> .wp-block-group.has-accent-1-background-color:first-child
	) {
	&:not(.primary-navigation-open) {
		.c-site-logo {
			img {
				width: auto;

				&.dark {
					display: none;
					opacity: 0;
				}

				&.light {
					display: block;
					opacity: 1;
				}
			}
		}

		.c-site-header__links {
			a {
				color: color(white);

				&:not(:hover):not(:focus) {
					color: color(white-rgba-075);

					&::before {
						background-color: color(white-rgba-075);
					}
				}

				&::before {
					background-color: color(white);
				}
			}
		}
	}

	.c-site-header {
		background-color: color(accent-1);

		&.is-sticky {
			background-color: color(accent-1);
		}

		/* Overlay */
		&::before {
			display: block;
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 0;
			background: linear-gradient(
				0deg,
				rgba(1, 19, 64, 0) 0%,
				rgba(0, 20, 71, 0.3) 100%
			);
			content: "";
		}
	}

	#page::before {
		display: block;
	}
}

/* Transparent fixed header on homepage */
body.home {
	.site {
		position: relative;

		.c-site-header {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			background-color: transparent !important;

			/* No overlay */
			&::before {
				content: none !important;
			}
		}
	}
}

/* Hero Header */
body:not(.primary-navigation-open) {
	.c-site-header.transparent {
		@mixin transition background-color;

		&:not(.is-sticky) {
			background-color: transparent;

			.site-logo.dark {
				display: none;
				opacity: 0;
			}

			.site-logo.light {
				display: block;
				opacity: 1;
			}

			.c-site-header__links {
				a {
					color: color(white);

					&::before {
						background-color: color(white);
					}
				}
			}
		}
	}
}

/* User is logged in, account for WP admin bar */
body.admin-bar {
	#page::before {
		height: calc(var(--header-height) + var(--wp-admin-bar));
	}

	.c-site-header {
		top: var(--wp-admin-bar);
	}

	.c-site-header__nav {
		margin-top: var(--wp-admin-bar);
	}
}

/*------------------------------
Mobile Drawer Toggled
------------------------------*/

/* Lock the page */
html:has(.primary-navigation-open) {
	&,
	body {
		width: 100%;
		overflow: hidden;
	}
}

body.primary-navigation-open {
	&:not(.home) {
		margin-top: var(--header-height);
	}

	.c-site-header {
		position: fixed;
	}

	/* Show the hamburger overlay */
	.c-site-header__hamburger-overlay {
		@mixin transition transform, 1600ms, transition(custom-bezier-function);
		transform: scale(100, 100);
		scale: none;
	}

	/* Show the mobile drawer */
	.c-site-header__nav {
		@mixin transition opacity, 1000ms, transition(custom-bezier-function),
			600ms;
		left: 0;
		opacity: 1;
		overflow: auto;
	}
}

/* Global Reach Fix Start */
@media(min-width:1025px) {
	#header-primary-menu .menu-wrapper > li:nth-of-type(2) ul.sub-menu {
		gap: 0.5rem;
	}
}
/* Global Reach Fix End */
