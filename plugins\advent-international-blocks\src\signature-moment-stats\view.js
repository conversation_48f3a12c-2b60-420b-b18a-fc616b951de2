// We use this function to add a transition and prevent this transition from being used before the page loads.
window.addEventListener('load', function () {
	document
		.querySelectorAll('.wp-block-advent-signature-stats .wp-block-heading')
		.forEach((el) => {
			el.classList.add('animation1-5_ease-in-out');
		});
	document
		.querySelectorAll('.wp-block-advent-signature-stats .wp-block-columns')
		.forEach((el) => {
			el.classList.add('animation1-5_ease-in-out');
		});
	document
		.querySelectorAll(
			'.wp-block-advent-signature-stats .wp-block-advent-signature-stats__foreground'
		)
		.forEach((el) => {
			el.classList.add('animation500ms_ease-in-out');
		});
});
