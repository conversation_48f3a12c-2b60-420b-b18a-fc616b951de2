.wp-block-advent-signature-moment-stats {
	--transition: all 1.2s transition(custom-bezier-function);
	position: relative;
	transition: var(--transition);
	background-position: 46% 75%;
	background-size: 150%;
	background-color: color(black);
	min-height: 100vh;

	&.wp-block-advent-signature-moment-stats--has-white-text-color {
		.eyebrow,
		.statistic,
		.wp-block-heading {
			color: color(white);
		}
	}

	&.wp-block-advent-signature-moment-stats--has-black-text-color {
		.eyebrow,
		.statistic,
		.wp-block-heading {
			color: color(black);
		}
	}

	&.wp-block-advent-signature-moment-stats--has-blue-text-color {
		.eyebrow,
		.statistic,
		.wp-block-heading {
			@mixin heading-gradient;
		}
	}

	&.wp-block-advent-signature-moment-stats--has-dark-blue-text-color {
		.eyebrow,
		.statistic,
		.wp-block-heading {
			color: color(accent-1);
		}
	}

	&.wp-block-advent-signature-moment-stats--has-gold-text-color {
		.eyebrow,
		.statistic,
		.wp-block-heading {
			color: color(accent-2);
		}
	}

	.animation1-5_ease-in-out,
	.animation500ms_ease-in-out {
		transition: var(--transition);
	}

	.statistic {
		border-top: 1px solid rgba(255, 255, 255, 0.15);
		font-size: clamp(4rem, 3.529vw + 2.676rem, 5.5rem);
		line-height: 1;
		font-family: typography(font-family-heading);
	}

	.eyebrow {
		margin-bottom: 0.5rem;
		font-size: 0.625rem;
		white-space: nowrap;

		@media (--tablet-portrait-up) {
			font-size: font-size(x-small);
		}
	}

	/* Foreground controls content layout and border animation */
	.wp-block-advent-signature-moment-stats__foreground {
		display: flex;
		position: relative;
		flex-direction: column;
		justify-content: space-between;
		z-index: 2;
		padding: spacing(xl) grid(gutter);
		height: 100%;

		@media (--tablet-portrait-up) {
			padding: spacing(2-xl) spacing(xl);
		}

		.wp-block-heading,
		.wp-block-columns {
			z-index: 9;
			color: color(white);
		}

		.wp-block-heading {
			font-size: clamp(2.5rem, 5.882vw + 0.294rem, 5rem);
			line-height: 1.2;
		}

		.wp-block-columns {
			display: flex !important;
			flex-wrap: wrap !important;
			align-items: center;

			.wp-block-column {
				flex-shrink: 0;
				width: 40%;

				@media (--tablet-landscape-up) {
					flex-shrink: 1;
					width: 25%;
				}
			}

			&:not(.is-not-stacked-on-mobile) > .wp-block-column {
				flex-basis: auto !important;

				@media (--tablet-landscape-up) {
					flex-basis: 0 !important;
				}
			}
		}
	}

	/* Animation overlay controls blur + mask animation */
	.wp-block-advent-signature-moment-stats__animation {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 2;
		width: 100%;
		height: 100%;

		/* Smaller screens - display the final state with no animation */

		--size: 26rem 26rem;
		@media (--phone-up) {
			--size: 36rem 36rem;
		}
		@media (--tablet-portrait-up) {
			--size: 40rem 40rem;
		}
		@media (--tablet-landscape-down) {
			backdrop-filter: blur(1.25rem);
			-webkit-mask:
				radial-gradient(#000 70%, #0000 70%) content-box 50% /
					var(--size, 0 0) no-repeat,
				linear-gradient(#000 0 0);
			mask:
				radial-gradient(#000 70%, #0000 70%) content-box 50% /
					var(--size, 0 0) no-repeat,
				linear-gradient(#000 0 0);
			-webkit-mask-composite: xor;
			mask-composite: exclude;
		}
	}

	/* Animate */
	&.wp-block-advent-signature-moment-stats--animate {
		.wp-block-advent-signature-moment-stats__foreground {
			padding: 1rem;

			/* Foreground default state */

			/* Desktop - borders used to shrink content */
			@media (--tablet-landscape-up) {
				transition: var(--transition);
				border-top: calc((100vh - 45rem) / 2) solid color(white);
				border-right: calc((100vw - grid(width)) / 2) solid color(white);
				border-bottom: calc((100vh - 45rem) / 2) solid color(white);
				border-left: calc((100vw - grid(width)) / 2) solid color(white);
				padding: 3.75rem;

				/* Content hidden */
				.wp-block-heading,
				.wp-block-columns {
					opacity: 0;
					transition: var(--transition);
					margin: 0;
					padding: 0;
				}

				.wp-block-heading {
					transform-origin: top left;
					scale: 0.85;
				}
			}
		}

		/* Hover - Desktop */
		&:hover {
			@media (--tablet-landscape-up) {
				background-position: 46% 80%;

				/* Overlay becomes blurred and circle mask animates in */
				.wp-block-advent-signature-moment-stats__animation {
					--size: 46rem 46rem;
					transform-origin: content-box 50% / var(--size, 0 0);
					backdrop-filter: blur(1.25rem);
					-webkit-mask:
						radial-gradient(#000 70%, #0000 70%) content-box 50% /
							var(--size, 0 0) no-repeat,
						linear-gradient(#000 0 0);
					mask:
						radial-gradient(#000 70%, #0000 70%) content-box 50% /
							var(--size, 0 0) no-repeat,
						linear-gradient(#000 0 0);
					mask-origin: content-box 50% / var(--size, 0 0);
					-webkit-mask-composite: xor;
					mask-composite: exclude;
					transition: all 1.8s transition(custom-bezier-function);
				}

				/* Foreground grows to reveal content */
				.wp-block-advent-signature-moment-stats__foreground {
					transition: var(--transition);
					border-top: 0 solid color(white);
					border-right: 0 solid color(white);
					border-bottom: 0 solid color(white);
					border-left: 0 solid color(white);

					/* Content scales into view */
					.wp-block-heading {
						scale: 1;
						opacity: 1;
						padding-top: 0;
					}

					.wp-block-columns {
						opacity: 1;
						padding-bottom: 0;
					}
				}
			}
		}
	}
}
