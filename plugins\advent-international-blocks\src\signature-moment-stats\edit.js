import { InnerBlocks, useBlockProps } from '@wordpress/block-editor';
import { useEffect } from '@wordpress/element';
import BackgroundImageStyle from '../utils/components/BackgroundImageStyle';
import Inspector from './inspector';
import TEMPLATE from './template';

export default function (props) {
	const { clientId, attributes, setAttributes } = props;

	useEffect(() => {
		setAttributes({
			uniqueClassName: `wp-block-advent-signature-moment-stats--${clientId}`,
		});
	}, [clientId]);

	const { className } = attributes;

	const classNames = [
		className || '',
		`alignfull`,
		attributes.uniqueClassName,
		`wp-block-advent-signature-moment-stats--has-${attributes.textColor.toLowerCase()}-text-color`,
	];

	const blockProps = useBlockProps({
		className: classNames.filter(Boolean).join(' '),
	});

	return (
		<>
			<Inspector {...props} />
			<div {...blockProps}>
				<div className="wp-block-advent-signature-moment-stats__foreground">
					<div className="wp-block-advent-signature-moment-stats__animation"></div>
					<InnerBlocks template={TEMPLATE} templateLock="all" />
					<BackgroundImageStyle
						selectorClass={attributes.uniqueClassName}
						url={attributes.image?.url}
					/>
				</div>
			</div>
		</>
	);
}
